import pandas as pd
import pymysql
from pymysql.err import OperationalError

def update_cover_values(config):
    try:
        # Read Excel file (changed from CSV to Excel)
        df = pd.read_excel('cover.xlsx')
        
        # Validate required columns
        required_columns = ['LOC_CD', 'GRP_NM', 'DPT_NM', 'CLSS_NM', 'SUB_CLSS_NM', 'COVER']
        if not all(col in df.columns for col in required_columns):
            raise ValueError(f"Missing required columns: {', '.join([col for col in required_columns if col not in df.columns])}")
        
        # Connect to database with proper parameters
        conn = pymysql.connect(
            host=config['host'],
            user=config['user'],
            password=config['password'],
            database=config['database'],
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        
        cursor = conn.cursor()
        
        # Create temporary table for updates
        temp_table_sql = """
        CREATE TEMPORARY TABLE cover_updates (
            LOC_CD VARCHAR(255),
            GRP_NM VARCHAR(255),
            DPT_NM VARCHAR(255),
            CLSS_NM VARCHAR(255),
            SUB_CLSS_NM VARCHAR(255),
            COVER DECIMAL(32,1)
        )
        """
        cursor.execute(temp_table_sql)
        
        # Insert data into temporary table
        for _, row in df.iterrows():
            insert_sql = """
            INSERT INTO cover_updates VALUES (%s, %s, %s, %s, %s, %s)
            """
            cursor.execute(insert_sql, (
                row['LOC_CD'],
                row['GRP_NM'],
                row['DPT_NM'],
                row['CLSS_NM'],
                row['SUB_CLSS_NM'],
                row['COVER']
            ))
        
        # Update main table
        update_sql = """
        UPDATE app_hb_preopt a
        JOIN cover_updates b ON 
            a.LOC_CD = b.LOC_CD AND
            a.GRP_NM = b.GRP_NM AND
            a.DPT_NM = b.DPT_NM AND
            a.CLSS_NM = b.CLSS_NM AND
            a.SUB_CLSS_NM = b.SUB_CLSS_NM
        SET a.COVER = COALESCE(b.COVER, 0)
        """
        cursor.execute(update_sql)
        
        # Commit changes
        conn.commit()
        
        print("Update completed successfully")
        
    except OperationalError as e:
        print(f"Database error: {e}")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        cursor.close()
        conn.close()

# Example usage
config = {
    'host': 'localhost',
    'user': 'prakhar',
    'password': 'landmark489',
    'database': 'space_optimization_db'
}

update_cover_values(config)