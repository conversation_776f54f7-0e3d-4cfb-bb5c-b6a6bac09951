import os
import sys
from datetime import datetime
from urllib.parse import quote_plus

import pandas as pd
import numpy as np
# from sqlalchemy import create_engine

# Add project root to import shared modules
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Local module imports
from shared_config import get_config
from utils import normalize, filter_reference_period
from db_writer import DBWriter

# Load configuration
cfg = get_config()

GROUP_COLS = ['LOC_CD', 'GRP_NM', 'DPT_NM', 'CLSS_NM', 'SUB_CLSS_NM']
UPPER_COLS = ['GRP_NM', 'DPT_NM', 'CLSS_NM', 'SUB_CLSS_NM']
COVER_RELATED_COLS = ['COVER', 'ROS']

class DataPreparator:
    def __init__(self, cfg):
        self.cfg = cfg

    def load_and_filter(self):
        # df = pd.read_excel(self.cfg['SALES_FILE'])

        sales_soh_customer_table = self.cfg["DATABASE_CONFIG"]["RESULT_TABLES"]["SALES_SOH_CUSTOMER_DATA"]
        print("sales_soh_customer_table is :", sales_soh_customer_table)
        writer = DBWriter(self.cfg)
        df = writer.read(        
        table_name=sales_soh_customer_table,
        # latest_by="created_at",
        latest_only=True)

        df['MONTH'] = pd.to_numeric(df['MONTH'], errors='coerce')
        df[UPPER_COLS] = df[UPPER_COLS].apply(lambda col: col.str.upper())
        df = df[df['RTL_QTY'] > 0]
        df = df[~df['LOC_CD'].isin(self.cfg['CLUSTER_DROP_LOC_CD'])]
        df = df[df['MNTH_AVG_SOH'] >= 0]
        df = df[df['GRP_NM'] == self.cfg['GROUP_FILTER']]

        df.sort_values(by=GROUP_COLS + ['MONTH'], inplace=True)
        df[COVER_RELATED_COLS] = df.groupby(GROUP_COLS)[COVER_RELATED_COLS].transform('last')

        return df


class FeatureEngineer:
    def __init__(self, cfg):
        self.cfg = cfg

    def add_min_lm_column(self, df):
        idx = df.groupby(GROUP_COLS)['MONTH'].idxmax()
        latest = df.loc[idx]
        latest['MIN_LM'] = (latest['TOTAL_LM'] / latest['MNTH_AVG_SOH']) * (3 * latest['ROS'])
        return df.merge(latest[GROUP_COLS + ['MIN_LM']], on=GROUP_COLS, how='left')

    def calculate_features(self, df):
        print("COVER null count:", df['COVER'].isna().sum())
        print("COVER unique values:", df['COVER'].unique())
        print("COVER_INVERSE unique values:", df['COVER_INVERSE'].unique())

        df['COVER_INVERSE'] = 1 / df['COVER']
        df['D_SALES'] = df['NET_SLS_AMT'] / df['STR_VISITS']
        df['D_UNITS'] = df['RTL_QTY'] / df['STR_VISITS']
        df['D_MARGIN_ABS'] = (df['GMV'] / df['NET_SLS_AMT']) * (df['NET_SLS_AMT'] / df['STR_VISITS'])

        for col in ['GMV_PER_LM', 'D_SALES', 'D_MARGIN_ABS', 'SPC', 'ASP', 'MARGIN_PERC', 'SLS_PER_INV']:
            df[f'ND_{col}'] = df.groupby(['LOC_CD', 'MONTH'])[col].transform(normalize)

        df['ND_COVER'] = df.groupby(['LOC_CD'])['COVER_INVERSE'].transform(normalize)
        df['LOG_SPACE'] = np.log(df['TOTAL_LM'] + 1)

        return df

    def add_performance_score(self, df):
        print("Adding performance score...")
        w = self.cfg['WEIGHTS']
        print(df[["D_UNITS", "CUST_PEN", "ND_MARGIN_PERC", "ND_GMV_PER_LM", "ND_COVER", "ND_ASP"]].isna().sum())
        print(df[["D_UNITS", "CUST_PEN", "ND_MARGIN_PERC", "ND_GMV_PER_LM", "ND_COVER", "ND_ASP"]].head())

        df['Performance'] = (
            w['d_units'] * df['D_UNITS'] +
            w['sub_cpen'] * df['CUST_PEN'] +
            w['gmv_margin_pct'] * df['ND_MARGIN_PERC'] +
            w['gmv_per_lm'] * df['ND_GMV_PER_LM'] +
            w['cover'] * df['ND_COVER'] +
            w['asp'] * df['ND_ASP']
        )
        print("After adding performance:", df.shape)
        print("Performance null count:", df["Performance"].isna().sum())
        print(df["Performance"].head())

        return df


class DataPreparationPipeline:
    def __init__(self, cfg):
        self.cfg = cfg
        self.preparator = DataPreparator(cfg)
        self.engineer = FeatureEngineer(cfg)

    def run(self):
        df = self.preparator.load_and_filter()
        df = self.engineer.add_min_lm_column(df)
        df = self.engineer.calculate_features(df)
        df = self.engineer.add_performance_score(df)

        # Add CLUSTER_NUM from so_clustering_result_hb table
        writer = DBWriter(self.cfg)
        try:
            # Read cluster mapping table
            cluster_df = writer.read("so_clustering_result_hb")

            # Validate required columns exist
            required_cols = {'LOC_CD', 'CLUSTER_NUM'}
            if not required_cols.issubset(cluster_df.columns):
                raise KeyError(f"Missing required columns in so_clustering_result_hb: {required_cols - set(cluster_df.columns)}")

            # Normalize LOC_CD type in both DataFrames
            df['LOC_CD'] = df['LOC_CD'].astype(str)
            cluster_df['LOC_CD'] = cluster_df['LOC_CD'].astype(str)

            # Ensure cluster_df LOC_CD is unique (safe even if already unique)
            cluster_map = cluster_df.drop_duplicates('LOC_CD')

            # Merge: keeps all rows in main df
            df = df.merge(
                cluster_map[['LOC_CD', 'CLUSTER_NUM']],
                on='LOC_CD',
                how='left'
            )
            # Debug: detect mismatches
            unmatched_main = sorted(set(df['LOC_CD']) - set(cluster_map['LOC_CD']))
            unmatched_cluster = sorted(set(cluster_map['LOC_CD']) - set(df['LOC_CD']))
            if unmatched_main:
                print(f"LOC_CD in main_df but not in cluster_df: {unmatched_main}")
            if unmatched_cluster:
                print(f"LOC_CD in cluster_df but not in main_df: {unmatched_cluster}")

        except Exception as e:
            print(f"Error merging CLUSTER_NUM from so_clustering_result_hb: {e}")


        # Write to database
        writer.write(df, "MAIN_OUTPUT")


        # Filter reference and write
        reference_df = filter_reference_period(
            df,
            self.cfg['REFERENCE_MONTHS'],
            self.cfg['REFERENCE_OUTPUT_FILE']
        )
        print('shape of the reference_df is :',reference_df.shape)

        writer.write(reference_df, "REFERENCE_PERIOD_DATA")
        return df
    
if __name__ == "__main__":
    pipeline = DataPreparationPipeline(cfg)
    pipeline.run()
